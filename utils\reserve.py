from utils import AES_Encrypt, enc, generate_captcha_key
import json
import requests
import re
import time
import logging
import datetime
from urllib3.exceptions import InsecureRequestWarning

def wait_until_specific_time(target_time, reserve_next_day=False):
    target_hour, target_minute, target_second = map(int, target_time.split(':'))
    now = datetime.datetime.now()
    current_seconds = now.hour * 3600 + now.minute * 60 + now.second
    target_seconds = target_hour * 3600 + target_minute * 60 + target_second
    
    if target_seconds < current_seconds and reserve_next_day:
        target_seconds += 24 * 3600
    elif target_seconds < current_seconds:
        logging.info(f"目标时间 {target_time} 已过，立即执行")
        return
    
    seconds_to_wait = target_seconds - current_seconds
    
    if seconds_to_wait > 10:
        minutes_to_wait = seconds_to_wait // 60
        seconds_remainder = seconds_to_wait % 60
        logging.info(f"需要等待 {minutes_to_wait} 分 {seconds_remainder} 秒")
        time.sleep(seconds_to_wait - 5)
        while datetime.datetime.now().hour != target_hour or datetime.datetime.now().minute != target_minute or datetime.datetime.now().second != target_second:
            time.sleep(0.1)
    else:
        time.sleep(0.2)
    return

def get_date(day_offset: int=0):
    today = datetime.datetime.now().date()
    offset_day = today + datetime.timedelta(days=day_offset)
    tomorrow = offset_day.strftime("%Y-%m-%d")
    return tomorrow

class reserve:
    def __init__(self, sleep_time=0.2, max_attempt=50, enable_slider=False, reserve_next_day=False):
        self.login_page = "https://passport2.chaoxing.com/mlogin?loginType=1&newversion=true&fid="
        self.url = "https://office.chaoxing.com/front/third/apps/seat/select?id={}&seatNum={}"
        self.submit_url = "https://office.chaoxing.com/data/apps/seat/submit"
        self.seat_url = "https://office.chaoxing.com/data/apps/seat/getusedtimes"
        self.login_url = "https://passport2.chaoxing.com/fanyalogin"
        self.token = ""
        self.success_times = 0
        self.fail_dict = []
        self.submit_msg = []
        # 优化网络连接配置
        self.requests = requests.session()
        self._setup_connection_pool()

        self.token_pattern = re.compile("token = '(.*?)'")
        self.headers = {
            "Referer": "https://office.chaoxing.com/",
            "Host": "captcha.chaoxing.com",
            "Connection": "keep-alive",
            "Keep-Alive": "timeout=120, max=100",
            "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
        }
        self.login_headers = {
            "Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "User-Agent": "Mozilla/5.0 (iPhone; CPU iPhone OS 10_3_1 like Mac OS X) AppleWebKit/603.1.3 (KHTML, like Gecko) Version/10.0 Mobile/14E304 Safari/602.1 wechatdevtools/1.05.2109131 MicroMessenger/8.0.5 Language/zh_CN webview/16364215743155638",
            "X-Requested-With": "XMLHttpRequest",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "Host": "passport2.chaoxing.com",
            "Connection": "keep-alive",
            "Keep-Alive": "timeout=120, max=100"
        }

        self.sleep_time = sleep_time
        self.max_attempt = max_attempt
        self.enable_slider = enable_slider
        self.reserve_next_day = reserve_next_day
        self._connection_preheated = False
        requests.packages.urllib3.disable_warnings(InsecureRequestWarning)

    def _setup_connection_pool(self):
        """配置高性能连接池"""
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry

        # 配置重试策略
        retry_strategy = Retry(
            total=3,
            backoff_factor=0.1,
            status_forcelist=[429, 500, 502, 503, 504],
            allowed_methods=["HEAD", "GET", "POST"]
        )

        # 配置连接池适配器
        adapter = HTTPAdapter(
            pool_connections=10,    # 连接池大小
            pool_maxsize=20,        # 最大连接数
            max_retries=retry_strategy,
            pool_block=False        # 非阻塞连接池
        )

        self.requests.mount("https://", adapter)
        self.requests.mount("http://", adapter)

        # 设置全局超时
        self.requests.timeout = (2, 3)  # 连接超时2s，读取超时3s

    def _preheat_connection(self, url):
        """预热网络连接，减少关键时刻的延迟"""
        if self._connection_preheated:
            logging.info("连接已预热，跳过")
            return

        try:
            logging.info(f"开始预热连接到: {url}")
            # 发送HEAD请求预热连接，不获取内容
            response = self.requests.head(url, timeout=(1, 2))
            self._connection_preheated = True
            logging.info(f"网络连接预热完成，状态码: {response.status_code}")
        except Exception as e:
            logging.warning(f"连接预热失败: {e}")
            # 预热失败不影响主流程


    # login and page token
    def _get_page_token(self, url):
        try:
            response = self.requests.get(url=url, verify=False)
            html = response.content.decode('utf-8')

            # 提取 token 值 (格式: token = 'xxx')
            token_match = re.search(r'token = \'([^\']+)\'', html)
            token = token_match.group(1) if token_match else ""

            # 提取 submit_enc 值 (格式: <input id="submit_enc" value="xxx"/>)
            submit_enc_match = re.search(r'id="submit_enc"[^>]*value="([^"]*)"', html)
            submit_enc = submit_enc_match.group(1) if submit_enc_match else ""

            return token, submit_enc
        except Exception as e:
            logging.error(f"获取页面 token 和 submit_enc 失败: {e}")
            return "", ""



    def get_login_status(self):
        self.requests.headers = self.login_headers
        self.requests.get(url=self.login_page, verify=False)

    def login(self, username, password):
        username = AES_Encrypt(username)
        password = AES_Encrypt(password)
        parm = {
            "fid": -1,
            "uname": username,
            "password": password,
            "refer": "http%3A%2F%2Foffice.chaoxing.com%2Ffront%2Fthird%2Fapps%2Fseat%2Fcode%3Fid%3D4219%26seatNum%3D380",
            "t": True
        }
        jsons = self.requests.post(
            url=self.login_url, params=parm, verify=False)
        obj = jsons.json()
        if obj['status']:
            logging.info(f"User {username} login successfully")
            return (True, '')
        else:
            logging.info(f"User {username} login failed. Please check you password and username! ")
            return (False, obj['msg2'])

    # extra: get roomid
    def roomid(self, encode):
        url = f"https://office.chaoxing.com/data/apps/seat/room/list?cpage=1&pageSize=100&firstLevelName=&secondLevelName=&thirdLevelName=&deptIdEnc={encode}"
        json_data = self.requests.get(url=url).content.decode('utf-8')
        ori_data = json.loads(json_data)
        for i in ori_data["data"]["seatRoomList"]:
            info = f'{i["firstLevelName"]}-{i["secondLevelName"]}-{i["thirdLevelName"]} id为：{i["id"]}'
            print(info)

    # solve captcha 

    def resolve_captcha(self):
        logging.info(f"Start to resolve captcha token")
        captcha_token, bg, tp = self.get_slide_captcha_data()
        logging.info(f"Successfully get prepared captcha_token {captcha_token}")
        logging.info(f"Captcha Image URL-small {tp}, URL-big {bg}")
        x = self.x_distance(bg, tp)
        logging.info(f"Successfully calculate the captcha distance {x}")

        params = {
            "callback": "jQuery33109180509737430778_1716381333117",
            "captchaId": "42sxgHoTPTKbt0uZxPJ7ssOvtXr3ZgZ1",
            "type": "slide",
            "token": captcha_token,
            "textClickArr": json.dumps([{"x": x}]),
            "coordinate": json.dumps([]),
            "runEnv": "10",
            "version": "1.1.18",
            "_": int(time.time() * 1000)
        }
        response = self.requests.get(
            f'https://captcha.chaoxing.com/captcha/check/verification/result', params=params, headers=self.headers)
        text = response.text.replace('jQuery33109180509737430778_1716381333117(', "").replace(')', "")
        data = json.loads(text)
        logging.info(f"Successfully resolve the captcha token {data}")
        try: 
           validate_val = json.loads(data["extraData"])['validate']
           return validate_val
        except KeyError as e:
            logging.info("Can't load validate value. Maybe server return mistake.")
            return ""

    def get_slide_captcha_data(self):
        url = "https://captcha.chaoxing.com/captcha/get/verification/image"
        timestamp = int(time.time() * 1000)
        capture_key, token = generate_captcha_key(timestamp)
        referer = f"https://office.chaoxing.com/front/third/apps/seat/code?id=3993&seatNum=0199"
        params = {
            "callback": f"jQuery33107685004390294206_1716461324846",
            "captchaId": "42sxgHoTPTKbt0uZxPJ7ssOvtXr3ZgZ1",
            "type": "slide",
            "version": "1.1.18",
            "captchaKey": capture_key,
            "token": token,
            "referer": referer,
            "_": timestamp,
            "d": "a",
            "b": "a"
        }
        response = self.requests.get(url=url, params=params, headers=self.headers)
        content = response.text
        
        data = content.replace("jQuery33107685004390294206_1716461324846(",
                            ")").replace(")", "")
        data = json.loads(data)
        captcha_token = data["token"]
        bg = data["imageVerificationVo"]["shadeImage"]
        tp = data["imageVerificationVo"]["cutoutImage"]
        return captcha_token, bg, tp
    
    def x_distance(self, bg, tp):
        import numpy as np
        import cv2
        def cut_slide(slide):
            slider_array = np.frombuffer(slide, np.uint8)
            slider_image = cv2.imdecode(slider_array, cv2.IMREAD_UNCHANGED)
            slider_part = slider_image[:, :, :3]
            mask = slider_image[:, :, 3]
            mask[mask != 0] = 255
            x, y, w, h = cv2.boundingRect(mask)
            cropped_image = slider_part[y:y + h, x:x + w]
            return cropped_image
        c_captcha_headers = {
            "Referer": "https://office.chaoxing.com/",
            "Host": "captcha-b.chaoxing.com",
            "Pragma" : 'no-cache',
            "Sec-Ch-Ua": '"Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
            'Sec-Ch-Ua-Mobile':'?0',
            'Sec-Ch-Ua-Platform':'"Linux"',
            'Sec-Fetch-Dest':'document',
            'Sec-Fetch-Mode':'navigate',
            'Sec-Fetch-Site':'none',
            'Sec-Fetch-User':'?1',
            'Upgrade-Insecure-Requests':'1',
            'User-Agent':'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36'
        }
        bgc, tpc = self.requests.get(bg, headers=c_captcha_headers), self.requests.get(tp, headers=c_captcha_headers)
        bg, tp = bgc.content, tpc.content 
        bg_img = cv2.imdecode(np.frombuffer(bg, np.uint8), cv2.IMREAD_COLOR)  
        tp_img = cut_slide(tp)
        bg_edge = cv2.Canny(bg_img, 100, 200)
        tp_edge = cv2.Canny(tp_img, 100, 200)
        bg_pic = cv2.cvtColor(bg_edge, cv2.COLOR_GRAY2RGB)
        tp_pic = cv2.cvtColor(tp_edge, cv2.COLOR_GRAY2RGB)
        res = cv2.matchTemplate(bg_pic, tp_pic, cv2.TM_CCOEFF_NORMED)
        _, _, _, max_loc = cv2.minMaxLoc(res)  
        tl = max_loc
        return tl[0]

    def submit(self, times, roomid, seatid, action, submit_time=None):
        # 提前获取验证码，避免在关键时刻进行耗时操作
        captcha = self.resolve_captcha() if self.enable_slider else ""
        logging.info(f"Pre-acquired captcha token: {captcha}")

        # 预热网络连接，减少关键时刻延迟
        self._preheat_connection(self.submit_url)

        for seat in seatid:
            suc = False
            while not suc and self.max_attempt > 0:
                # token获取移到get_submit方法中，在target_time时刻执行
                suc = self.get_submit(self.submit_url, times=times, roomid=roomid, seatid=seat,
                                   captcha=captcha, action=action, submit_time=submit_time)
                if suc:
                    return suc
                time.sleep(self.sleep_time)
                self.max_attempt -= 1
        return suc

    def sign(self):
        try:
            # 先获取预约列表
            response = self.requests.get(
                url='https://office.chaoxing.com/data/apps/seat/reservelist?'
                'indexId=0&'
                'pageSize=100&'
                'type=-1'
            ).json()
            
            if 'data' not in response:
                logging.info(f"Failed to get reservation list: {response}")
                return []
            
            reserveList = response['data']['reserveList']
            result = []
            
            # 遍历预约列表并尝试签到
            for reservation in reserveList:
                if reservation['type'] == -1:
                    if reservation['today'] == get_date(0) or reservation['today'] == get_date(1):
                        # 获取预约ID并尝试签到
                        reservation_id = reservation['id']
                        sign_response = self.requests.get(
                            url=f'https://office.chaoxing.com/data/apps/seat/sign?id={reservation_id}'
                        ).json()
                        
                        reservation['sign_status'] = '签到成功' if sign_response.get('success') else sign_response.get('msg', '签到失败')
                        result.append(reservation)
                        logging.info(f"Seat {reservation['seatNum']} sign status: {reservation['sign_status']}")
                        
            return result
        except Exception as e:
            logging.error(f"Error during sign process: {e}")
            return []
    
    # 获取到最近一次预约的座位ID
    def get_my_seat_id(self):
        # seatId 不一定为602 仅为演示
        if self.version == 0:
            response = \
                self.session.get(url='https://office.chaoxing.com/data/apps/seatengine/reservelist?seatId=602').json()[
                    'data']['reserveList']
        else:
            response = self.session.get(url='https://office.chaoxing.com/data/apps/seat/reservelist?'
                                            'indexId=0&'
                                            'pageSize=100&'
                                            'type=-1').json()['data']['reserveList']
        result = []
        for index in response:
            if index['type'] == -1:
                if index['today'] == self.today or index['today'] == self.tomorrow:
                    result.append(index)
        return result 

    def get_submit(self, url, times, roomid, seatid, captcha="", action=False, submit_time=None):
        # 确定预约日期：如果 reserve_next_day 为 True，则预约明天，否则预约今天
        delta_day = 1 if self.reserve_next_day else 0
        day = datetime.datetime.now().date() + datetime.timedelta(days=0 + delta_day)  # 预约今天，days=0
        if action:
            day = datetime.datetime.now().date() + datetime.timedelta(days=1 + delta_day)  # 由于时区问题，action 模式下日期加一天

        # 优化请求头配置，减少不必要的头部信息
        headers = {
            "Host": "office.chaoxing.com",
            "Connection": "keep-alive",
            "Keep-Alive": "timeout=120, max=100",
            "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
            "User-Agent": self.login_headers["User-Agent"],
            "X-Requested-With": "XMLHttpRequest"
        }
        max_retries = 3  # 增加重试次数到3次，提高成功率
        retry_interval = 0.2  # 减少重试间隔到0.2秒，加快重试速度

        # 循环尝试请求，最多尝试 max_retries 次
        for attempt in range(max_retries):
            try:
                # 只有在第一次尝试时执行 submit_time 等待逻辑，避免重试时重复等待
                if submit_time and attempt == 0:
                    logging.info(f"等待直到指定时间 {submit_time} 再进行提交...")  # 记录等待开始的日志
                    wait_until_specific_time(submit_time)  # 等待直到指定的提交时间，适用于定时任务

                    logging.info("开始准备开始获取token并发送请求")  # 记录请求开始的日志

                # 在target_time时刻获取token和submit_enc
                token, submit_enc_value = self._get_page_token(self.url.format(roomid, seatid))
                logging.info(f"Get token: {token}")
                logging.info(f"Get submit_enc: {submit_enc_value}")

                # 构建请求参数，包括房间ID、开始时间、结束时间、日期、座位号、验证码和token
                parm = {
                    "roomId": roomid,
                    "startTime": times[0],
                    "endTime": times[1],
                    "day": str(day),
                    "seatNum": seatid,
                    "captcha": captcha,
                    "token": token
                }
                logging.info(f"submit parameter {parm} ")  # 记录请求参数以便调试
                parm["enc"] = enc(parm, submit_enc_value)  # 对参数进行加密处理，生成 enc 字段
                logging.info("开始提交预约请求...")  # 记录等待结束，正式提交请求的日志

                # 优化的POST请求：使用data而不是params，减少URL编码开销
                response = self.requests.post(
                    url=url,
                    data=parm,  # 使用data传参，更适合POST请求
                    timeout=(1.5, 2.5),  # 优化超时设置：连接1.5s，读取2.5s
                    headers=headers,  # 使用预设的请求头
                    stream=False  # 不使用流式传输，减少延迟
                )
                html = response.content.decode('utf-8')
                logging.info("已发送")  # 记录请求成功发送的日志
                # 将响应内容（时间段 + 响应结果）添加到提交消息列表中
                self.submit_msg.append(times[0] + "~" + times[1] + ':  ' + str(json.loads(html)))
                logging.info(json.loads(html))  # 记录响应内容以便调试
                return json.loads(html)["success"]  # 返回请求是否成功的标志
            except (requests.exceptions.ConnectionError, requests.exceptions.Timeout) as e:
                # 捕获连接错误或超时异常，记录错误信息
                logging.error(f"连接错误或超时: {str(e)}")
                if attempt < max_retries - 1:  # 如果不是最后一次尝试，则等待后重试
                    time.sleep(retry_interval)  # 等待指定的重试间隔时间（0.5 秒），避免立即重试
                else:
                    # 如果已达到最大重试次数，记录失败信息并返回 False 表示提交失败
                    logging.error("重试失败，已达到最大重试次数")
                    return False
