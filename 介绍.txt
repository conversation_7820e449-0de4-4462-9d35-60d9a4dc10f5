config为安徽财经大学

53 14 * * *  /www/server/cron/3ab48c27ec99cb9787749c362afae517 >> /www/server/cron/3ab48c27ec99cb9787749c362afae517.log 2>
0 0 * * * ~/auto_cert_renewal.sh
0 21 * * * python3 /root/ChaoXingReserveSeat-rebuild/main.py -m debug -u /root/ChaoXingReserveSeat-rebuild/config.json > >
0 7 * * * python3 /root/ChaoXingReserveSeat-rebuild/main_today.py -m debug -u /root/ChaoXingReserveSeat-rebuild/config2.j>
0 21 * * * python3 /root/ChaoXingReserveSeat-rebuild/main.py -m debug -u /root/ChaoXingReserveSeat-rebuild/config.json > >
0 7 * * * python3 /root/ChaoXingReserveSeat-rebuild/main_today.py -m debug -u /root/ChaoXingReserveSeat-rebuild/config2.j>
30 22 * * * python3 /root/ChaoXingReserveSeat-rebuild/main.py -m debug -u /root/ChaoXingReserveSeat-rebuild/config3.json >
0 20 * * * python3 /root/ChaoXingReserveSeat-rebuild/main.py -m debug -u /root/ChaoXingReserveSeat-rebuild/config4.json >>
0 20 * * * python3 /root/ChaoXingReserveSeat-rebuild/main.py -m debug -u /root/ChaoXingReserveSeat-rebuild/config5.json >>
0 20 * * * python3 /root/ChaoXingReserveSeat-rebuild/main.py -m debug -u /root/ChaoXingReserveSeat-rebuild/config4.json >>
0 20 * * * python3 /root/ChaoXingReserveSeat-rebuild/main.py -m debug -u /root/ChaoXingReserveSeat-rebuild/config5.json >>
0 7 * * * python3 /root/ChaoXingReserveSeat-rebuild/main_today.py -m debug -u /root/ChaoXingReserveSeat-rebuild/config6.j>








