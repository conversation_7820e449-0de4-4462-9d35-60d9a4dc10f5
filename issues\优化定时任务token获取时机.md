# 优化定时任务token获取时机

## 任务背景
解决定时任务系统中token在target_time之前获取导致时间窗口失效的问题。

## 问题分析
- 当前流程：获取token → 获取验证码 → 等待target_time → 提交请求
- 问题：token只能在特定时间窗口内有效，提前获取会失效
- 验证码获取耗时较长（2-5秒），包含图像处理

## 解决方案
采用分阶段Token获取策略：
- 验证码提前获取（独立于页面token）
- 页面token在target_time时刻获取
- 确保token在有效时间窗口内使用

## 实施计划

### 步骤1：重构submit()方法
- 位置：utils/reserve.py 第236-250行
- 将验证码获取移到循环外，提前执行
- 移除循环内的token获取逻辑
- 简化参数传递

### 步骤2：增强get_submit()方法  
- 位置：utils/reserve.py 第307行开始
- 在等待target_time后立即获取token
- 添加token获取重试机制
- 优化时间控制逻辑

### 步骤3：新增辅助方法
- 创建_get_token_just_in_time()方法
- 专门处理target_time时刻的token获取
- 包含重试和错误处理

## 预期效果
- 解决token时间窗口问题
- 优化验证码获取时机
- 保持target_time功能有效性
- 提高预约成功率

## 实施完成情况

### ✅ 已完成的修改

1. **重构submit()方法** (第236-251行)
   - 将验证码获取移到循环外，提前执行
   - 移除了循环内的token获取逻辑
   - 简化了参数传递，只传递必要参数

2. **新增_get_token_just_in_time()方法** (第103-124行)
   - 专门处理target_time时刻的token获取
   - 包含重试机制（最多3次，间隔0.5秒）
   - 完善的错误处理和日志记录

3. **增强get_submit()方法** (第331-410行)
   - 修改方法签名，移除token和submit_enc_value参数
   - 在等待target_time后立即获取token
   - 优化了参数构建和错误处理

### ✅ 验证结果
- 代码语法检查通过
- 程序能够正常启动和运行
- 多线程调用正常工作
- 保持了原有功能的完整性

### 🎯 优化效果
- **时间控制精确**：token在target_time时刻获取，确保在有效窗口内
- **验证码预获取**：避免在关键时刻进行耗时的图像处理
- **容错能力增强**：token获取失败时自动重试
- **日志记录完善**：便于调试和监控
