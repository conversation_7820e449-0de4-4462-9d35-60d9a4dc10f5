# This is a basic workflow that is manually triggered

name: auto_Reserve

# Controls when the action will run. Workflow runs when manually triggered using the UI
# or API.
on:
  schedule:
    - cron: "59 21 * * 0,1,2,3,4" # 周一到周五每天早上5:59启动（防止github action过于高负载）
  workflow_dispatch:
  
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
  
      - name: Set up Python 3.11
        uses: actions/setup-python@v2
        with:
          python-version: 3.11
      - name: install dependency
        run: |
          python -m pip install --upgrade pip
          sudo apt-get install build-essential libssl-dev libffi-dev  python3-dev -y
          pip install cryptography requests opencv-python

      - name: run script
        env:
          USERNAMES: ${{ secrets.USERNAMES }}
          PASSWORDS: ${{ secrets.PASSWORDS }}
        run: |
          python main.py -m debug --action


